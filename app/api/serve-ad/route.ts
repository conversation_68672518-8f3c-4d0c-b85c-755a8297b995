import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { appId, appSecret, topics, userContext } = await request.json()

    // Validate required fields
    if (!appId || !appSecret) {
      return NextResponse.json(
        { error: 'appId and appSecret are required' },
        { status: 400 }
      )
    }

    // Verify app credentials
    const app = await prisma.app.findUnique({
      where: { appId },
      select: {
        id: true,
        appSecret: true,
        status: true,
        name: true
      }
    })

    if (!app || app.appSecret !== appSecret || app.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'Invalid credentials or inactive app' },
        { status: 401 }
      )
    }

    // Find matching advertisements
    let whereClause: any = {
      status: 'ACTIVE'
    }

    // If topics are provided, try to match them
    if (topics && Array.isArray(topics) && topics.length > 0) {
      whereClause.targetTopics = {
        hasSome: topics
      }
    }

    const ads = await prisma.advertisement.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        bidType: true,
        bidAmount: true
      },
      take: 10 // Limit results
    })

    if (ads.length === 0) {
      // If no targeted ads found, get any active ads
      const fallbackAds = await prisma.advertisement.findMany({
        where: { status: 'ACTIVE' },
        select: {
          id: true,
          name: true,
          description: true,
          imageUrl: true,
          productUrl: true,
          targetTopics: true,
          bidType: true,
          bidAmount: true
        },
        take: 5
      })

      if (fallbackAds.length === 0) {
        return NextResponse.json(
          { message: 'No ads available' },
          { status: 204 }
        )
      }

      // Select random ad from fallback
      const selectedAd = fallbackAds[Math.floor(Math.random() * fallbackAds.length)]
      
      return NextResponse.json({
        ad: {
          id: selectedAd.id,
          name: selectedAd.name,
          description: selectedAd.description,
          imageUrl: selectedAd.imageUrl,
          productUrl: selectedAd.productUrl,
          targetTopics: selectedAd.targetTopics
        },
        appId: app.id,
        trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/impressions`
      })
    }

    // Simple ad selection algorithm (can be improved with ML/bidding logic)
    // For now, prioritize by bid amount and randomize
    const weightedAds = ads.map(ad => ({
      ...ad,
      weight: Number(ad.bidAmount) * (Math.random() * 0.5 + 0.75) // Add some randomness
    }))

    // Sort by weight (higher bid + randomness = higher chance)
    weightedAds.sort((a, b) => b.weight - a.weight)
    
    const selectedAd = weightedAds[0]

    return NextResponse.json({
      ad: {
        id: selectedAd.id,
        name: selectedAd.name,
        description: selectedAd.description,
        imageUrl: selectedAd.imageUrl,
        productUrl: selectedAd.productUrl,
        targetTopics: selectedAd.targetTopics
      },
      appId: app.id,
      trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/impressions`,
      instructions: {
        impression: 'POST to trackingUrl with { adId, appId, clicked: false }',
        click: 'POST to trackingUrl with { adId, appId, clicked: true }'
      }
    })
  } catch (error) {
    console.error('Ad serving error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint for testing/documentation
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'AI Ad Platform - Ad Serving API',
    version: '1.0.0',
    endpoints: {
      'POST /api/serve-ad': {
        description: 'Request an advertisement for your AI application',
        required: ['appId', 'appSecret'],
        optional: ['topics', 'userContext'],
        example: {
          appId: 'app_xxxxxxxxxxxxxxxx',
          appSecret: 'secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          topics: ['AI', 'productivity', 'automation'],
          userContext: {
            userAgent: 'Mozilla/5.0...',
            language: 'en-US'
          }
        }
      },
      'POST /api/impressions': {
        description: 'Track ad impressions and clicks',
        required: ['adId', 'appId'],
        optional: ['clicked', 'userAgent'],
        example: {
          adId: 'ad_xxxxxxxxxxxxxxxx',
          appId: 'app_xxxxxxxxxxxxxxxx',
          clicked: false,
          userAgent: 'Mozilla/5.0...'
        }
      }
    }
  })
}
