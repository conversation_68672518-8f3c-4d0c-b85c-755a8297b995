import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { Decimal } from '@prisma/client/runtime/library'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user.roles.includes('ADVERTISER')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const ads = await prisma.advertisement.findMany({
      where: {
        userId: session.user.id
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // For now, return mock analytics data
    const adsWithAnalytics = ads.map(ad => ({
      ...ad,
      budget: Number(ad.budget),
      bidAmount: Number(ad.bidAmount),
      impressions: Math.floor(Math.random() * 5000),
      clicks: Math.floor(Math.random() * 200),
      spend: Math.random() * Number(ad.budget) * 0.8
    }))

    return NextResponse.json({ ads: adsWithAnalytics })
  } catch (error) {
    console.error('Ads fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user.roles.includes('ADVERTISER')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { 
      name, 
      description, 
      productUrl, 
      targetTopics, 
      budget, 
      bidType, 
      bidAmount 
    } = await request.json()

    // Validate input
    if (!name || !description || !productUrl || !budget || !bidType || !bidAmount) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(productUrl)
    } catch {
      return NextResponse.json(
        { error: 'Invalid product URL format' },
        { status: 400 }
      )
    }

    // Validate bid type
    if (!['CPC', 'CPM'].includes(bidType)) {
      return NextResponse.json(
        { error: 'Invalid bid type' },
        { status: 400 }
      )
    }

    // Validate numeric values
    if (isNaN(budget) || budget <= 0) {
      return NextResponse.json(
        { error: 'Budget must be a positive number' },
        { status: 400 }
      )
    }

    if (isNaN(bidAmount) || bidAmount <= 0) {
      return NextResponse.json(
        { error: 'Bid amount must be a positive number' },
        { status: 400 }
      )
    }

    const ad = await prisma.advertisement.create({
      data: {
        userId: session.user.id,
        name,
        description,
        productUrl,
        targetTopics: Array.isArray(targetTopics) ? targetTopics : [],
        budget: new Decimal(budget),
        bidType,
        bidAmount: new Decimal(bidAmount)
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        createdAt: true,
      }
    })

    return NextResponse.json(
      { 
        message: 'Advertisement created successfully', 
        ad: {
          ...ad,
          budget: Number(ad.budget),
          bidAmount: Number(ad.bidAmount),
          impressions: 0,
          clicks: 0,
          spend: 0
        }
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Ad creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
