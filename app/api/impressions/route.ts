import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { adId, appId, clicked, userAgent } = await request.json()

    // Validate required fields
    if (!adId || !appId) {
      return NextResponse.json(
        { error: 'adId and appId are required' },
        { status: 400 }
      )
    }

    // Get client IP address
    const forwarded = request.headers.get('x-forwarded-for')
    const ipAddress = forwarded ? forwarded.split(',')[0] : 
                     request.headers.get('x-real-ip') || 
                     'unknown'

    // Verify that the ad and app exist
    const [ad, app] = await Promise.all([
      prisma.advertisement.findUnique({
        where: { id: adId },
        select: { id: true, status: true }
      }),
      prisma.app.findUnique({
        where: { id: appId },
        select: { id: true, status: true }
      })
    ])

    if (!ad || ad.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'Advertisement not found or inactive' },
        { status: 404 }
      )
    }

    if (!app || app.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'App not found or inactive' },
        { status: 404 }
      )
    }

    // Create impression record
    const impression = await prisma.adImpression.create({
      data: {
        adId,
        appId,
        clicked: Boolean(clicked),
        ipAddress,
        userAgent: userAgent || null
      }
    })

    return NextResponse.json(
      { 
        message: 'Impression recorded successfully',
        impressionId: impression.id
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Impression tracking error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const appId = searchParams.get('appId')
    const adId = searchParams.get('adId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    if (!appId && !adId) {
      return NextResponse.json(
        { error: 'Either appId or adId is required' },
        { status: 400 }
      )
    }

    const whereClause: any = {}
    
    if (appId) whereClause.appId = appId
    if (adId) whereClause.adId = adId
    
    if (startDate && endDate) {
      whereClause.timestamp = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const impressions = await prisma.adImpression.findMany({
      where: whereClause,
      select: {
        id: true,
        timestamp: true,
        clicked: true,
        ipAddress: true,
        advertisement: {
          select: {
            id: true,
            name: true
          }
        },
        app: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: 1000 // Limit to prevent large responses
    })

    const stats = {
      totalImpressions: impressions.length,
      totalClicks: impressions.filter(imp => imp.clicked).length,
      clickThroughRate: impressions.length > 0 
        ? ((impressions.filter(imp => imp.clicked).length / impressions.length) * 100).toFixed(2)
        : '0.00'
    }

    return NextResponse.json({
      impressions,
      stats
    })
  } catch (error) {
    console.error('Impressions fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
