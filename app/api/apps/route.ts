import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { nanoid } from 'nanoid'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user.roles.includes('MODEL_PROVIDER')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const apps = await prisma.app.findMany({
      where: {
        userId: session.user.id
      },
      select: {
        id: true,
        name: true,
        appId: true,
        status: true,
        createdAt: true,
        description: true,
        callbackUrl: true,
        // Add aggregated impression data later
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // For now, return mock analytics data
    const appsWithAnalytics = apps.map(app => ({
      ...app,
      impressions: Math.floor(Math.random() * 10000),
      clicks: Math.floor(Math.random() * 500),
      revenue: Math.random() * 100
    }))

    return NextResponse.json({ apps: appsWithAnalytics })
  } catch (error) {
    console.error('Apps fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user.roles.includes('MODEL_PROVIDER')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { name, callbackUrl, description } = await request.json()

    // Validate input
    if (!name || !callbackUrl) {
      return NextResponse.json(
        { error: 'Name and callback URL are required' },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(callbackUrl)
    } catch {
      return NextResponse.json(
        { error: 'Invalid callback URL format' },
        { status: 400 }
      )
    }

    // Generate unique app ID and secret
    const appId = `app_${nanoid(16)}`
    const appSecret = `secret_${nanoid(32)}`

    const app = await prisma.app.create({
      data: {
        userId: session.user.id,
        name,
        appId,
        appSecret,
        callbackUrl,
        description: description || null
      },
      select: {
        id: true,
        name: true,
        appId: true,
        status: true,
        createdAt: true,
        description: true,
        callbackUrl: true
      }
    })

    return NextResponse.json(
      { 
        message: 'App created successfully', 
        app: {
          ...app,
          impressions: 0,
          clicks: 0,
          revenue: 0
        }
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('App creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
