'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Card, CardBody, CardHeader } from '@heroui/card'
import { Button } from '@heroui/button'
import { Chip } from '@heroui/chip'
import { Divider } from '@heroui/divider'
import { Link } from '@heroui/link'

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-default-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="max-w-4xl mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Account Settings</h1>
        <p className="text-default-600 mt-2">
          Manage your account preferences and access
        </p>
      </div>

      <div className="grid gap-6">
        {/* Account Information */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Account Information</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div>
              <p className="text-sm text-default-600">Email Address</p>
              <p className="font-medium">{session.user.email}</p>
            </div>
            
            <div>
              <p className="text-sm text-default-600 mb-2">Account Roles</p>
              <div className="flex gap-2">
                {session.user.roles.map((role) => (
                  <Chip
                    key={role}
                    color={role === 'MODEL_PROVIDER' ? 'primary' : 'secondary'}
                    variant="flat"
                  >
                    {role === 'MODEL_PROVIDER' ? 'Model Provider' : 'Advertiser'}
                  </Chip>
                ))}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Dashboard Access */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Dashboard Access</h2>
          </CardHeader>
          <CardBody>
            <div className="grid md:grid-cols-2 gap-4">
              {session.user.roles.includes('MODEL_PROVIDER') && (
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Model Provider Dashboard</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Manage your AI applications and view revenue analytics
                  </p>
                  <Button
                    as={Link}
                    href="/dashboard/model"
                    color="primary"
                    variant="flat"
                    size="sm"
                  >
                    Go to Dashboard
                  </Button>
                </div>
              )}
              
              {session.user.roles.includes('ADVERTISER') && (
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Advertiser Dashboard</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Create and manage your advertising campaigns
                  </p>
                  <Button
                    as={Link}
                    href="/dashboard/advertiser"
                    color="secondary"
                    variant="flat"
                    size="sm"
                  >
                    Go to Dashboard
                  </Button>
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Security */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Security</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Password</p>
                <p className="text-sm text-default-600">
                  Last updated: Not available
                </p>
              </div>
              <Button variant="bordered" size="sm" isDisabled>
                Change Password
              </Button>
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Two-Factor Authentication</p>
                <p className="text-sm text-default-600">
                  Add an extra layer of security to your account
                </p>
              </div>
              <Button variant="bordered" size="sm" isDisabled>
                Enable 2FA
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Account Actions */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Account Actions</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Export Data</p>
                <p className="text-sm text-default-600">
                  Download a copy of your account data
                </p>
              </div>
              <Button variant="bordered" size="sm" isDisabled>
                Export Data
              </Button>
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-danger">Delete Account</p>
                <p className="text-sm text-default-600">
                  Permanently delete your account and all data
                </p>
              </div>
              <Button color="danger" variant="bordered" size="sm" isDisabled>
                Delete Account
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}
