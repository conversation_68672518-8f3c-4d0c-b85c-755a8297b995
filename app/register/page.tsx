'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardBody, CardHeader } from '@heroui/card'
import { Input } from '@heroui/input'
import { Button } from '@heroui/button'
import { Link } from '@heroui/link'
import { Checkbox } from '@heroui/checkbox'
import { Chip } from '@heroui/chip'

export default function RegisterPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [roles, setRoles] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()

  const handleRoleToggle = (role: string) => {
    setRoles(prev => 
      prev.includes(role) 
        ? prev.filter(r => r !== role)
        : [...prev, role]
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    // Validation
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    if (roles.length === 0) {
      setError('Please select at least one role')
      setIsLoading(false)
      return
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          roles,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Account created successfully! Redirecting to login...')
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      } else {
        setError(data.error || 'An error occurred')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 text-center">
          <h1 className="text-2xl font-bold">Create Account</h1>
          <p className="text-default-600">
            Join AI Ad Platform today
          </p>
        </CardHeader>
        <CardBody>
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            {error && (
              <div className="p-3 text-sm text-danger bg-danger/10 rounded-lg">
                {error}
              </div>
            )}
            
            {success && (
              <div className="p-3 text-sm text-success bg-success/10 rounded-lg">
                {success}
              </div>
            )}
            
            <Input
              type="email"
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              isDisabled={isLoading}
            />
            
            <Input
              type="password"
              label="Password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              isDisabled={isLoading}
              description="Must be at least 8 characters long"
            />
            
            <Input
              type="password"
              label="Confirm Password"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              isDisabled={isLoading}
            />
            
            <div className="space-y-3">
              <p className="text-sm font-medium">Select your role(s):</p>
              <div className="flex flex-col gap-2">
                <div 
                  className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-default-50"
                  onClick={() => handleRoleToggle('MODEL_PROVIDER')}
                >
                  <Checkbox
                    isSelected={roles.includes('MODEL_PROVIDER')}
                    onChange={() => handleRoleToggle('MODEL_PROVIDER')}
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Model Provider</span>
                      <Chip size="sm" color="primary" variant="flat">Earn Revenue</Chip>
                    </div>
                    <p className="text-xs text-default-600">
                      Monetize your AI applications with ads
                    </p>
                  </div>
                </div>
                
                <div 
                  className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-default-50"
                  onClick={() => handleRoleToggle('ADVERTISER')}
                >
                  <Checkbox
                    isSelected={roles.includes('ADVERTISER')}
                    onChange={() => handleRoleToggle('ADVERTISER')}
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Advertiser</span>
                      <Chip size="sm" color="secondary" variant="flat">Reach Users</Chip>
                    </div>
                    <p className="text-xs text-default-600">
                      Advertise to AI application users
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <Button
              type="submit"
              color="primary"
              isLoading={isLoading}
              className="w-full"
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </Button>
            
            <div className="text-center text-sm text-default-600">
              Already have an account?{' '}
              <Link href="/login" color="primary">
                Sign in
              </Link>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  )
}
