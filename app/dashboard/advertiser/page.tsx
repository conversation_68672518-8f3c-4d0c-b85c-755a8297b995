'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardBody, CardHeader } from '@heroui/card'
import { Button } from '@heroui/button'
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from '@heroui/table'
import { Chip } from '@heroui/chip'
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from '@heroui/modal'
import { Input } from '@heroui/input'
import { Select, SelectItem } from '@heroui/select'
import { Progress } from '@heroui/progress'
import FileUpload from '@/components/file-upload'
import PerformanceChart from '@/components/analytics/performance-chart'
import TopPerformers from '@/components/analytics/top-performers'

interface Advertisement {
  id: string
  name: string
  description: string
  imageUrl?: string
  productUrl: string
  targetTopics: string[]
  budget: number
  bidType: 'CPC' | 'CPM'
  bidAmount: number
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED'
  createdAt: string
  impressions?: number
  clicks?: number
  spend?: number
}

export default function AdvertiserDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { isOpen, onOpen, onOpenChange } = useDisclosure()

  const [ads, setAds] = useState<Advertisement[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [analytics, setAnalytics] = useState<any>(null)

  // Form state
  const [adName, setAdName] = useState('')
  const [description, setDescription] = useState('')
  const [productUrl, setProductUrl] = useState('')
  const [targetTopics, setTargetTopics] = useState('')
  const [budget, setBudget] = useState('')
  const [bidType, setBidType] = useState<'CPC' | 'CPM'>('CPC')
  const [bidAmount, setBidAmount] = useState('')
  const [imageUrl, setImageUrl] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
      return
    }

    if (session && !session.user.roles.includes('ADVERTISER')) {
      router.push('/dashboard/model')
      return
    }

    if (session) {
      fetchAds()
    }
  }, [session, status, router])

  const fetchAds = async () => {
    try {
      const [adsResponse, analyticsResponse] = await Promise.all([
        fetch('/api/ads'),
        fetch('/api/analytics/advertiser')
      ])

      if (adsResponse.ok) {
        const data = await adsResponse.json()
        setAds(data.ads || [])
      }

      if (analyticsResponse.ok) {
        const data = await analyticsResponse.json()
        setAnalytics(data.analytics)
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      const response = await fetch('/api/ads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: adName,
          description,
          productUrl,
          imageUrl: imageUrl || null,
          targetTopics: targetTopics.split(',').map(t => t.trim()).filter(Boolean),
          budget: parseFloat(budget),
          bidType,
          bidAmount: parseFloat(bidAmount),
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setAds(prev => [...prev, data.ad])
        // Reset form
        setAdName('')
        setDescription('')
        setProductUrl('')
        setImageUrl('')
        setTargetTopics('')
        setBudget('')
        setBidAmount('')
        onOpenChange()
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to create advertisement')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-default-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!session || !session.user.roles.includes('ADVERTISER')) {
    return null
  }

  const totalBudget = ads.reduce((sum, ad) => sum + ad.budget, 0)
  const totalSpend = ads.reduce((sum, ad) => sum + (ad.spend || 0), 0)
  const totalImpressions = ads.reduce((sum, ad) => sum + (ad.impressions || 0), 0)
  const totalClicks = ads.reduce((sum, ad) => sum + (ad.clicks || 0), 0)

  return (
    <div className="max-w-7xl mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Advertiser Dashboard</h1>
          <p className="text-default-600 mt-2">
            Create and manage your advertising campaigns
          </p>
        </div>
        <Button color="primary" onPress={onOpen}>
          Create New Ad
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-primary">{ads.length}</p>
            <p className="text-default-600">Active Campaigns</p>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-success">
              {totalImpressions.toLocaleString()}
            </p>
            <p className="text-default-600">Total Impressions</p>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-warning">
              {totalClicks.toLocaleString()}
            </p>
            <p className="text-default-600">Total Clicks</p>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-secondary">
              ${totalSpend.toFixed(2)} / ${totalBudget.toFixed(2)}
            </p>
            <p className="text-default-600">Spend / Budget</p>
            <Progress
              value={(totalSpend / totalBudget) * 100}
              className="mt-2"
              color="secondary"
            />
          </CardBody>
        </Card>
      </div>

      {/* Analytics Charts */}
      {analytics && (
        <div className="grid lg:grid-cols-2 gap-6 mb-8">
          <PerformanceChart
            data={analytics.monthlyData || []}
            title="Campaign Performance Trends"
          />
          <TopPerformers
            items={analytics.topCampaigns || []}
            title="Top Performing Campaigns"
            type="campaigns"
          />
        </div>
      )}

      {/* Ads Table */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Your Campaigns</h2>
        </CardHeader>
        <CardBody>
          {ads.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-default-600 mb-4">No campaigns created yet</p>
              <Button color="primary" onPress={onOpen}>
                Create Your First Campaign
              </Button>
            </div>
          ) : (
            <Table aria-label="Ads table">
              <TableHeader>
                <TableColumn>CAMPAIGN NAME</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>BID TYPE</TableColumn>
                <TableColumn>BUDGET</TableColumn>
                <TableColumn>IMPRESSIONS</TableColumn>
                <TableColumn>CLICKS</TableColumn>
                <TableColumn>CTR</TableColumn>
                <TableColumn>SPEND</TableColumn>
              </TableHeader>
              <TableBody>
                {ads.map((ad) => {
                  const ctr = ad.impressions && ad.clicks
                    ? ((ad.clicks / ad.impressions) * 100).toFixed(2)
                    : '0.00'

                  return (
                    <TableRow key={ad.id}>
                      <TableCell className="font-medium">{ad.name}</TableCell>
                      <TableCell>
                        <Chip
                          color={
                            ad.status === 'ACTIVE' ? 'success' :
                              ad.status === 'PAUSED' ? 'warning' : 'default'
                          }
                          variant="flat"
                          size="sm"
                        >
                          {ad.status}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <Chip variant="flat" size="sm">
                          {ad.bidType} ${ad.bidAmount.toFixed(2)}
                        </Chip>
                      </TableCell>
                      <TableCell>${ad.budget.toFixed(2)}</TableCell>
                      <TableCell>{(ad.impressions || 0).toLocaleString()}</TableCell>
                      <TableCell>{(ad.clicks || 0).toLocaleString()}</TableCell>
                      <TableCell>{ctr}%</TableCell>
                      <TableCell>${(ad.spend || 0).toFixed(2)}</TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Create Ad Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="3xl">
        <ModalContent>
          {(onClose) => (
            <form onSubmit={handleSubmit}>
              <ModalHeader className="flex flex-col gap-1">
                Create New Advertisement
              </ModalHeader>
              <ModalBody>
                {error && (
                  <div className="p-3 text-sm text-danger bg-danger/10 rounded-lg">
                    {error}
                  </div>
                )}

                <div className="grid md:grid-cols-2 gap-4">
                  <Input
                    label="Campaign Name"
                    placeholder="My AI App Campaign"
                    value={adName}
                    onChange={(e) => setAdName(e.target.value)}
                    required
                    isDisabled={isSubmitting}
                  />

                  <Input
                    label="Product URL"
                    placeholder="https://your-product.com"
                    value={productUrl}
                    onChange={(e) => setProductUrl(e.target.value)}
                    required
                    isDisabled={isSubmitting}
                  />
                </div>

                <Input
                  label="Description"
                  placeholder="Brief description of your product or service"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  required
                  isDisabled={isSubmitting}
                />

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Advertisement Image (Optional)
                  </label>
                  <FileUpload
                    onUpload={setImageUrl}
                    currentImage={imageUrl}
                    className="w-full"
                  />
                </div>

                <Input
                  label="Target Topics"
                  placeholder="AI, machine learning, productivity (comma-separated)"
                  value={targetTopics}
                  onChange={(e) => setTargetTopics(e.target.value)}
                  isDisabled={isSubmitting}
                  description="Enter topics relevant to your target audience"
                />

                <div className="grid md:grid-cols-3 gap-4">
                  <Select
                    label="Bid Type"
                    selectedKeys={[bidType]}
                    onSelectionChange={(keys) => setBidType(Array.from(keys)[0] as 'CPC' | 'CPM')}
                    isDisabled={isSubmitting}
                  >
                    <SelectItem key="CPC" value="CPC">CPC (Cost Per Click)</SelectItem>
                    <SelectItem key="CPM" value="CPM">CPM (Cost Per Mille)</SelectItem>
                  </Select>

                  <Input
                    label={`Bid Amount (${bidType})`}
                    placeholder="0.50"
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                    required
                    isDisabled={isSubmitting}
                    startContent="$"
                    type="number"
                    step="0.01"
                    min="0.01"
                  />

                  <Input
                    label="Total Budget"
                    placeholder="100.00"
                    value={budget}
                    onChange={(e) => setBudget(e.target.value)}
                    required
                    isDisabled={isSubmitting}
                    startContent="$"
                    type="number"
                    step="0.01"
                    min="1.00"
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cancel
                </Button>
                <Button color="primary" type="submit" isLoading={isSubmitting}>
                  Create Campaign
                </Button>
              </ModalFooter>
            </form>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}
