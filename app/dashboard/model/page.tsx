'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardBody, CardHeader } from '@heroui/card'
import { Button } from '@heroui/button'
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from '@heroui/table'
import { Chip } from '@heroui/chip'
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from '@heroui/modal'
import { Input } from '@heroui/input'
import RevenueChart from '@/components/analytics/revenue-chart'
import TopPerformers from '@/components/analytics/top-performers'


interface App {
  id: string
  name: string
  appId: string
  status: 'ACTIVE' | 'SUSPENDED'
  createdAt: string
  impressions?: number
  clicks?: number
  revenue?: number
}

export default function ModelProviderDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { isOpen, onOpen, onOpenChange } = useDisclosure()

  const [apps, setApps] = useState<App[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [analytics, setAnalytics] = useState<any>(null)

  // Form state
  const [appName, setAppName] = useState('')
  const [callbackUrl, setCallbackUrl] = useState('')
  const [description, setDescription] = useState('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
      return
    }

    if (session && !session.user.roles.includes('MODEL_PROVIDER')) {
      router.push('/dashboard/advertiser')
      return
    }

    if (session) {
      fetchApps()
    }
  }, [session, status, router])

  const fetchApps = async () => {
    try {
      const [appsResponse, analyticsResponse] = await Promise.all([
        fetch('/api/apps'),
        fetch('/api/analytics/model')
      ])

      if (appsResponse.ok) {
        const data = await appsResponse.json()
        setApps(data.apps || [])
      }

      if (analyticsResponse.ok) {
        const data = await analyticsResponse.json()
        setAnalytics(data.analytics)
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      const response = await fetch('/api/apps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: appName,
          callbackUrl,
          description,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setApps(prev => [...prev, data.app])
        setAppName('')
        setCallbackUrl('')
        setDescription('')
        onOpenChange()
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to create app')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-default-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!session || !session.user.roles.includes('MODEL_PROVIDER')) {
    return null
  }

  return (
    <div className="max-w-7xl mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Model Provider Dashboard</h1>
          <p className="text-default-600 mt-2">
            Manage your AI applications and track revenue
          </p>
        </div>
        <Button color="primary" onPress={onOpen}>
          Register New App
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-primary">{apps.length}</p>
            <p className="text-default-600">Active Apps</p>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-success">
              {apps.reduce((sum, app) => sum + (app.impressions || 0), 0).toLocaleString()}
            </p>
            <p className="text-default-600">Total Impressions</p>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-warning">
              {apps.reduce((sum, app) => sum + (app.clicks || 0), 0).toLocaleString()}
            </p>
            <p className="text-default-600">Total Clicks</p>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <p className="text-2xl font-bold text-secondary">
              ${apps.reduce((sum, app) => sum + (app.revenue || 0), 0).toFixed(2)}
            </p>
            <p className="text-default-600">Total Revenue</p>
          </CardBody>
        </Card>
      </div>

      {/* Analytics Charts */}
      {analytics && (
        <div className="grid lg:grid-cols-2 gap-6 mb-8">
          <RevenueChart
            data={analytics.monthlyData || []}
            title="Monthly Revenue Trends"
          />
          <TopPerformers
            items={analytics.topApps || []}
            title="Top Performing Apps"
            type="apps"
          />
        </div>
      )}

      {/* Apps Table */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Your Applications</h2>
        </CardHeader>
        <CardBody>
          {apps.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-default-600 mb-4">No applications registered yet</p>
              <Button color="primary" onPress={onOpen}>
                Register Your First App
              </Button>
            </div>
          ) : (
            <Table aria-label="Apps table">
              <TableHeader>
                <TableColumn>APP NAME</TableColumn>
                <TableColumn>APP ID</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>IMPRESSIONS</TableColumn>
                <TableColumn>CLICKS</TableColumn>
                <TableColumn>REVENUE</TableColumn>
                <TableColumn>CREATED</TableColumn>
              </TableHeader>
              <TableBody>
                {apps.map((app) => (
                  <TableRow key={app.id}>
                    <TableCell className="font-medium">{app.name}</TableCell>
                    <TableCell>
                      <code className="text-xs bg-default-100 px-2 py-1 rounded">
                        {app.appId}
                      </code>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={app.status === 'ACTIVE' ? 'success' : 'danger'}
                        variant="flat"
                        size="sm"
                      >
                        {app.status}
                      </Chip>
                    </TableCell>
                    <TableCell>{(app.impressions || 0).toLocaleString()}</TableCell>
                    <TableCell>{(app.clicks || 0).toLocaleString()}</TableCell>
                    <TableCell>${(app.revenue || 0).toFixed(2)}</TableCell>
                    <TableCell>
                      {new Date(app.createdAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Register App Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="2xl">
        <ModalContent>
          {(onClose) => (
            <form onSubmit={handleSubmit}>
              <ModalHeader className="flex flex-col gap-1">
                Register New Application
              </ModalHeader>
              <ModalBody>
                {error && (
                  <div className="p-3 text-sm text-danger bg-danger/10 rounded-lg">
                    {error}
                  </div>
                )}

                <Input
                  label="Application Name"
                  placeholder="My AI App"
                  value={appName}
                  onChange={(e) => setAppName(e.target.value)}
                  required
                  isDisabled={isSubmitting}
                />

                <Input
                  label="Callback URL"
                  placeholder="https://your-app.com/api/ads"
                  value={callbackUrl}
                  onChange={(e) => setCallbackUrl(e.target.value)}
                  required
                  isDisabled={isSubmitting}
                  description="URL where we'll send ad requests"
                />

                <Input
                  label="Description (Optional)"
                  placeholder="Brief description of your AI application"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  isDisabled={isSubmitting}
                />
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cancel
                </Button>
                <Button color="primary" type="submit" isLoading={isSubmitting}>
                  Register App
                </Button>
              </ModalFooter>
            </form>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}
