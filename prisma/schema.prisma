// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  MODEL_PROVIDER
  ADVERTISER
}

enum AppStatus {
  ACTIVE
  SUSPENDED
}

enum BidType {
  CPC
  CPM
}

enum AdStatus {
  ACTIVE
  PAUSED
  COMPLETED
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  passwordHash  String
  roles         Role[]
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  apps          App[]
  advertisements Advertisement[]

  @@map("users")
}

model App {
  id          String    @id @default(cuid())
  userId      String
  name        String
  appId       String    @unique // Public identifier
  appSecret   String    // Private key
  callbackUrl String
  description String?
  status      AppStatus @default(ACTIVE)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  impressions AdImpression[]

  @@map("apps")
}

model Advertisement {
  id            String   @id @default(cuid())
  userId        String
  name          String
  description   String
  imageUrl      String?
  productUrl    String
  targetTopics  String[] // Array of topic tags
  budget        Decimal  @db.Decimal(10, 2)
  bidType       BidType
  bidAmount     Decimal  @db.Decimal(10, 4)
  status        AdStatus @default(ACTIVE)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  impressions AdImpression[]

  @@map("advertisements")
}

model AdImpression {
  id        String   @id @default(cuid())
  adId      String
  appId     String
  timestamp DateTime @default(now())
  clicked   Boolean  @default(false)
  ipAddress String?
  userAgent String?

  // Relations
  advertisement Advertisement @relation(fields: [adId], references: [id], onDelete: Cascade)
  app           App @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@map("ad_impressions")
}
