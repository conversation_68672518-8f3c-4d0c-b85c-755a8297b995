# AI Advertisement Management Platform MVP

A comprehensive web platform that connects AI model providers with advertisers for targeted advertising solutions. Built with Next.js 14+, PostgreSQL, and HeroUI components.

## 🚀 Features

### For AI Model Providers
- **Easy Integration**: Secure API credentials for seamless ad integration
- **Revenue Analytics**: Real-time tracking of impressions, clicks, and earnings
- **App Management**: Register and manage multiple AI applications
- **Competitive Rates**: Flexible CPM and CPC pricing models

### For Advertisers
- **Targeted Campaigns**: Reach specific AI application categories
- **Performance Tracking**: Detailed metrics and ROI analysis
- **Budget Management**: Flexible budget allocation and bidding strategies
- **High-Quality Audience**: Engaged AI application users

### Platform Features
- **Dual Role Support**: Users can be both providers and advertisers
- **Real-time Analytics**: Interactive charts and performance metrics
- **Secure Authentication**: NextAuth.js with role-based access
- **File Upload**: Image upload for advertisements
- **Rate Limiting**: Built-in API protection
- **Responsive Design**: Mobile-first UI with HeroUI components

## 🛠 Tech Stack

- **Frontend**: Next.js 14+ (App Router), React 18, TypeScript
- **UI Components**: HeroUI (NextUI successor)
- **Authentication**: NextAuth.js with credentials provider
- **Database**: PostgreSQL with Prisma ORM
- **Styling**: Tailwind CSS
- **Charts**: Recharts for analytics visualization
- **Validation**: Zod for schema validation
- **Runtime**: Bun (recommended) or Node.js 18+

## 📋 Prerequisites

- Bun runtime or Node.js 18+
- PostgreSQL database
- Git

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/ai-ad-platform.git
cd ai-ad-platform
```

### 2. Install Dependencies
```bash
# Using Bun (recommended)
bun install

# Or using npm
npm install
```

### 3. Environment Setup
Create a `.env.local` file in the root directory:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ai_ad_platform?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 4. Database Setup
```bash
# Generate Prisma client
bunx prisma generate

# Push schema to database
bunx prisma db push
```

### 5. Start Development Server
```bash
# Using Bun
bun run dev

# Or using npm
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📱 Usage

### Getting Started
1. **Register**: Create an account and select your role(s)
2. **Model Providers**: Register your AI apps and get API credentials
3. **Advertisers**: Create campaigns and upload ad creatives
4. **Integration**: Use the API to serve ads in your applications
5. **Analytics**: Monitor performance and optimize campaigns

### API Integration for Model Providers

#### 1. Get Ad for Your Application
```javascript
const response = await fetch('http://localhost:3000/api/serve-ad', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    appId: 'your-app-id',
    appSecret: 'your-app-secret',
    topics: ['AI', 'productivity'], // Optional targeting
    userContext: { // Optional user context
      userAgent: navigator.userAgent,
      language: 'en-US'
    }
  })
})

const { ad, trackingUrl } = await response.json()
```

#### 2. Track Impressions and Clicks
```javascript
// Track impression
await fetch(trackingUrl, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adId: ad.id,
    appId: 'your-app-id',
    clicked: false
  })
})

// Track click
await fetch(trackingUrl, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    adId: ad.id,
    appId: 'your-app-id',
    clicked: true
  })
})
```

## 🏗 Architecture

### Database Schema
- **Users**: Authentication and role management
- **Apps**: AI application registration and credentials
- **Advertisements**: Campaign data and targeting
- **AdImpressions**: Tracking data for analytics

### API Endpoints
- `/api/auth/*` - Authentication (NextAuth.js)
- `/api/apps` - App management for providers
- `/api/ads` - Advertisement management
- `/api/serve-ad` - Ad serving for integration
- `/api/impressions` - Impression tracking
- `/api/analytics/*` - Dashboard analytics
- `/api/upload` - File upload for ad images

### Security Features
- Rate limiting on all endpoints
- Input validation with Zod schemas
- CSRF protection
- Secure file upload with type validation
- Role-based access control

## 🚀 Deployment

See [deployment.md](./deployment.md) for detailed deployment instructions including:
- Environment configuration
- Database setup
- Docker deployment
- Vercel deployment
- Security considerations
- Monitoring and logging

## 🧪 Testing

```bash
# Run tests (when implemented)
bun test

# Type checking
bun run type-check

# Linting
bun run lint
```

## 📊 Analytics & Monitoring

The platform includes comprehensive analytics:
- **Revenue tracking** for model providers
- **Campaign performance** for advertisers
- **Real-time metrics** with interactive charts
- **Top performers** analysis
- **Click-through rates** and conversion tracking

## 🔒 Security

- **Authentication**: Secure session management
- **Authorization**: Role-based access control
- **Rate Limiting**: API protection against abuse
- **Input Validation**: Comprehensive data validation
- **File Upload**: Secure image handling
- **HTTPS**: SSL/TLS encryption in production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the [deployment guide](./deployment.md)
- Review the API documentation in the code
- Open an issue on GitHub

## 🗺 Roadmap

- [ ] Advanced targeting algorithms
- [ ] Real-time bidding system
- [ ] Machine learning optimization
- [ ] Mobile SDK for easier integration
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Payment processing integration
- [ ] A/B testing framework
